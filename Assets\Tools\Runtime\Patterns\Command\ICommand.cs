using System.Collections;
using UnityEngine;

namespace SmartVertex.Tools
{
    /// <summary>
    /// Represents a command with execute and undo operations.
    /// </summary>
    public interface ICommand
    {
        /// <summary>
        /// Executes the command logic.
        /// </summary>
        void Execute();

        /// <summary>
        /// Reverses the effects of the command.
        /// </summary>
        void Undo();
    }

    /// <summary>
    /// Represents an asynchronous command with execute and undo operations using Unity's Awaitable.
    /// </summary>
    public interface IAwaitableCommand
    {
        /// <summary>
        /// Executes the command logic asynchronously.
        /// </summary>
        /// <returns>An awaitable task representing the asynchronous operation.</returns>
        Awaitable ExecuteAsync();

        /// <summary>
        /// Reverses the effects of the command asynchronously.
        /// </summary>
        /// <returns>An awaitable task representing the asynchronous operation.</returns>
        Awaitable UndoAsync();
    }

    /// <summary>
    /// Represents a coroutine-based command with execute and undo operations using IEnumerator.
    /// </summary>
    public interface ICoroutineCommand
    {
        /// <summary>
        /// Executes the command logic as a coroutine.
        /// </summary>
        /// <returns>An IEnumerator for coroutine execution.</returns>
        IEnumerator ExecuteCoroutine();

        /// <summary>
        /// Reverses the effects of the command as a coroutine.
        /// </summary>
        /// <returns>An IEnumerator for coroutine execution.</returns>
        IEnumerator UndoCoroutine();
    }
}
