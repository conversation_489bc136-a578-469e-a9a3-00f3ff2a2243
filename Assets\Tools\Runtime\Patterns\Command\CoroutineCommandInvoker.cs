using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace SmartVertex.Tools
{
    /// <summary>
    /// Invokes, undoes, redoes, and manages a stack of coroutine commands.
    /// </summary>
    public class CoroutineCommandInvoker : MonoBehaviour
    {
        private readonly Stack<ICoroutineCommand> undoStack = new Stack<ICoroutineCommand>();
        private readonly Stack<ICoroutineCommand> redoStack = new Stack<ICoroutineCommand>();

        /// <summary>
        /// Gets a value indicating whether there are commands to undo.
        /// </summary>
        public bool CanUndo => undoStack.Count > 0;

        /// <summary>
        /// Gets a value indicating whether there are commands to redo.
        /// </summary>
        public bool CanRedo => redoStack.Count > 0;

        /// <summary>
        /// Executes a command as a coroutine and adds it to the undo stack. Clears the redo stack.
        /// </summary>
        /// <param name="command">The command to execute.</param>
        /// <returns>A coroutine that can be started with StartCoroutine.</returns>
        public IEnumerator ExecuteCommandCoroutine(ICoroutineCommand command)
        {
            yield return StartCoroutine(command.ExecuteCoroutine());
            undoStack.Push(command);
            redoStack.Clear();
        }

        /// <summary>
        /// Undoes the last executed command as a coroutine and adds it to the redo stack.
        /// </summary>
        /// <returns>A coroutine that can be started with StartCoroutine.</returns>
        public IEnumerator UndoLastCommandCoroutine()
        {
            if (undoStack.Count > 0)
            {
                var command = undoStack.Pop();
                yield return StartCoroutine(command.UndoCoroutine());
                redoStack.Push(command);
            }
        }

        /// <summary>
        /// Redoes the last undone command as a coroutine and adds it back to the undo stack.
        /// </summary>
        /// <returns>A coroutine that can be started with StartCoroutine.</returns>
        public IEnumerator RedoLastCommandCoroutine()
        {
            if (redoStack.Count > 0)
            {
                var command = redoStack.Pop();
                yield return StartCoroutine(command.ExecuteCoroutine());
                undoStack.Push(command);
            }
        }

        /// <summary>
        /// Returns the next command to be undone, or null if none.
        /// </summary>
        public ICoroutineCommand PeekUndo() => undoStack.Count > 0 ? undoStack.Peek() : null;

        /// <summary>
        /// Returns the next command to be redone, or null if none.
        /// </summary>
        public ICoroutineCommand PeekRedo() => redoStack.Count > 0 ? redoStack.Peek() : null;

        /// <summary>
        /// Clears the undo and redo history.
        /// </summary>
        public void ClearHistory()
        {
            undoStack.Clear();
            redoStack.Clear();
        }
    }
}
