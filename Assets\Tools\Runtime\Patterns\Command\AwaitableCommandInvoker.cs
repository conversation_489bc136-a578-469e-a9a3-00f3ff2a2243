using System.Collections.Generic;
using UnityEngine;

namespace SmartVertex.Tools
{
    /// <summary>
    /// Invokes, undoes, redoes, and manages a stack of awaitable commands.
    /// </summary>
    public class AwaitableCommandInvoker
    {
        private readonly Stack<IAwaitableCommand> undoStack = new Stack<IAwaitableCommand>();
        private readonly Stack<IAwaitableCommand> redoStack = new Stack<IAwaitableCommand>();

        /// <summary>
        /// Gets a value indicating whether there are commands to undo.
        /// </summary>
        public bool CanUndo => undoStack.Count > 0;

        /// <summary>
        /// Gets a value indicating whether there are commands to redo.
        /// </summary>
        public bool CanRedo => redoStack.Count > 0;

        /// <summary>
        /// Executes a command asynchronously and adds it to the undo stack. Clears the redo stack.
        /// </summary>
        /// <param name="command">The command to execute.</param>
        /// <returns>An awaitable task representing the asynchronous operation.</returns>
        public async Awaitable ExecuteCommandAsync(IAwaitableCommand command)
        {
            await command.ExecuteAsync();
            undoStack.Push(command);
            redoStack.Clear();
        }

        /// <summary>
        /// Undoes the last executed command asynchronously and adds it to the redo stack.
        /// </summary>
        /// <returns>An awaitable task representing the asynchronous operation.</returns>
        public async Awaitable UndoLastCommandAsync()
        {
            if (undoStack.Count > 0)
            {
                var command = undoStack.Pop();
                await command.UndoAsync();
                redoStack.Push(command);
            }
        }

        /// <summary>
        /// Redoes the last undone command asynchronously and adds it back to the undo stack.
        /// </summary>
        /// <returns>An awaitable task representing the asynchronous operation.</returns>
        public async Awaitable RedoLastCommandAsync()
        {
            if (redoStack.Count > 0)
            {
                var command = redoStack.Pop();
                await command.ExecuteAsync();
                undoStack.Push(command);
            }
        }

        /// <summary>
        /// Returns the next command to be undone, or null if none.
        /// </summary>
        public IAwaitableCommand PeekUndo() => undoStack.Count > 0 ? undoStack.Peek() : null;

        /// <summary>
        /// Returns the next command to be redone, or null if none.
        /// </summary>
        public IAwaitableCommand PeekRedo() => redoStack.Count > 0 ? redoStack.Peek() : null;

        /// <summary>
        /// Clears the undo and redo history.
        /// </summary>
        public void ClearHistory()
        {
            undoStack.Clear();
            redoStack.Clear();
        }
    }
}
