Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.0f1 (9ea152932a88) revision 10395986'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 14242 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-08-12T10:59:29Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.1.0f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/Development/Unity/UnityProjects/Packages
-logFile
Logs/AssetImportWorker1.log
-srvPort
11627
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: D:/Development/Unity/UnityProjects/Packages
D:/Development/Unity/UnityProjects/Packages
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [20164]  Target information:

Player connection [20164]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3073942934 [EditorId] 3073942934 [Version] 1048832 [Id] WindowsEditor(7,Ali) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [20164]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3073942934 [EditorId] 3073942934 [Version] 1048832 [Id] WindowsEditor(7,Ali) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [20164]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 3073942934 [EditorId] 3073942934 [Version] 1048832 [Id] WindowsEditor(7,Ali) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [20164]  * "[IP] *************** [Port] 0 [Flags] 2 [Guid] 3073942934 [EditorId] 3073942934 [Version] 1048832 [Id] WindowsEditor(7,Ali) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [20164]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 3073942934 [EditorId] 3073942934 [Version] 1048832 [Id] WindowsEditor(7,Ali) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [20164] Host joined multi-casting on [***********:54997]...
Player connection [20164] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 251.76 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.0f1 (9ea152932a88)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/Development/Unity/UnityProjects/Packages/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        AMD Radeon(TM) RX Vega 11 Graphics (ID=0x15d8)
    Vendor:          ATI
    VRAM:            7121 MB
    App VRAM Budget: 8439 MB
    Driver:          31.0.21921.1000
    Unified Memory Architecture
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56028
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.0f1/Editor/Data/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 11.426398 seconds.
- Loaded All Assemblies, in 66.842 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 6996 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 13.103 seconds
Domain Reload Profiling: 79901ms
	BeginReloadAssembly (23393ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (2209ms)
	RebuildNativeTypeToScriptingClass (32ms)
	initialDomainReloadingComplete (12642ms)
	LoadAllAssembliesAndSetupDomain (28522ms)
		LoadAssemblies (23403ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (28476ms)
			TypeCache.Refresh (28472ms)
				TypeCache.ScanAssembly (26778ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (13104ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (12688ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9868ms)
			SetLoadedEditorAssemblies (47ms)
			BeforeProcessingInitializeOnLoad (443ms)
			ProcessInitializeOnLoadAttributes (1757ms)
			ProcessInitializeOnLoadMethodAttributes (571ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in 16.750 seconds
Refreshing native plugins compatible for Editor in 2.98 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  4.520 seconds
Domain Reload Profiling: 21241ms
	BeginReloadAssembly (1598ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (25ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (90ms)
	RebuildCommonClasses (192ms)
	RebuildNativeTypeToScriptingClass (42ms)
	initialDomainReloadingComplete (248ms)
	LoadAllAssembliesAndSetupDomain (14640ms)
		LoadAssemblies (13655ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (2330ms)
			TypeCache.Refresh (2178ms)
				TypeCache.ScanAssembly (1977ms)
			BuildScriptInfoCaches (117ms)
			ResolveRequiredComponents (29ms)
	FinalizeReload (4521ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2523ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (38ms)
			SetLoadedEditorAssemblies (12ms)
			BeforeProcessingInitializeOnLoad (480ms)
			ProcessInitializeOnLoadAttributes (1763ms)
			ProcessInitializeOnLoadMethodAttributes (206ms)
			AfterProcessingInitializeOnLoad (24ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.09 seconds
Refreshing native plugins compatible for Editor in 3.73 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 217 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6645 unused Assets / (5.7 MB). Loaded Objects now: 7360.
Memory consumption went from 170.0 MB to 164.2 MB.
Total: 20.363600 ms (FindLiveObjects: 1.016200 ms CreateObjectMapping: 1.762400 ms MarkObjects: 11.039300 ms  DeleteObjects: 6.543800 ms)

========================================================================
Received Import Request.
  Time since last request: 109333.831664 seconds.
  path: Assets/Tools/Runtime/Utilities/GridUtility.cs
  artifactKey: Guid(69a3865ba5d762e4b88f6c3e08e451a6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/Utilities/GridUtility.cs using Guid(69a3865ba5d762e4b88f6c3e08e451a6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5807f32ca67990274fdd451ab7c8a804') in 0.0130338 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/Tools/Editor/GridUtilityTestWindow.cs
  artifactKey: Guid(943e20662e6cd9b409fdaf3bd8365b2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Editor/GridUtilityTestWindow.cs using Guid(943e20662e6cd9b409fdaf3bd8365b2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6f0e8c814c4a525144228e61f60af833') in 0.001003 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000048 seconds.
  path: Assets/Tools/Editor/GeminiAPITestWindow.cs
  artifactKey: Guid(ce5a2489dfe724143889913694844d86) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Editor/GeminiAPITestWindow.cs using Guid(ce5a2489dfe724143889913694844d86) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '00000000000000000000000000000000') in 0.0006616 seconds
Import Error Code:(4)
Message: Build asset version error: assets/tools/editor/geminiapitestwindow.cs in SourceAssetDB has modification time of '2025-08-12T10:44:20.1617974Z' while content on disk has modification time of '2025-08-12T11:01:11.9663348Z'
  ERROR: Build asset version error: assets/tools/editor/geminiapitestwindow.cs in SourceAssetDB has modification time of '2025-08-12T10:44:20.1617974Z' while content on disk has modification time of '2025-08-12T11:01:11.9663348Z'
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Tools/Runtime/SmartVertex.Tools.asmdef
  artifactKey: Guid(309e90cabdb087148872cfc4a2b91670) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Runtime/SmartVertex.Tools.asmdef using Guid(309e90cabdb087148872cfc4a2b91670) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '719875e62955401fff1b35215ed8bbd6') in 0.0011965 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/Tools/Editor/GeminiAPITestWindow.cs
  artifactKey: Guid(ce5a2489dfe724143889913694844d86) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Editor/GeminiAPITestWindow.cs using Guid(ce5a2489dfe724143889913694844d86) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '00000000000000000000000000000000') in 0.0007881 seconds
Import Error Code:(4)
Message: Build asset version error: assets/tools/editor/geminiapitestwindow.cs in SourceAssetDB has modification time of '2025-08-12T10:44:20.1617974Z' while content on disk has modification time of '2025-08-12T11:01:11.9663348Z'
  ERROR: Build asset version error: assets/tools/editor/geminiapitestwindow.cs in SourceAssetDB has modification time of '2025-08-12T10:44:20.1617974Z' while content on disk has modification time of '2025-08-12T11:01:11.9663348Z'
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.793 seconds
Refreshing native plugins compatible for Editor in 2.60 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.688 seconds
Domain Reload Profiling: 4474ms
	BeginReloadAssembly (465ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (16ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (110ms)
	RebuildCommonClasses (111ms)
	RebuildNativeTypeToScriptingClass (33ms)
	initialDomainReloadingComplete (73ms)
	LoadAllAssembliesAndSetupDomain (1103ms)
		LoadAssemblies (868ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (478ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (418ms)
			ResolveRequiredComponents (34ms)
	FinalizeReload (2688ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2139ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (35ms)
			SetLoadedEditorAssemblies (10ms)
			BeforeProcessingInitializeOnLoad (512ms)
			ProcessInitializeOnLoadAttributes (1398ms)
			ProcessInitializeOnLoadMethodAttributes (168ms)
			AfterProcessingInitializeOnLoad (15ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (38ms)
Refreshing native plugins compatible for Editor in 8.11 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 23 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6641 unused Assets / (4.0 MB). Loaded Objects now: 7380.
Memory consumption went from 145.0 MB to 141.0 MB.
Total: 41.755700 ms (FindLiveObjects: 1.494500 ms CreateObjectMapping: 2.141600 ms MarkObjects: 31.709000 ms  DeleteObjects: 6.407800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 51.111033 seconds.
  path: Assets/Tools/Editor/GeminiAPITestWindow.cs
  artifactKey: Guid(ce5a2489dfe724143889913694844d86) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Tools/Editor/GeminiAPITestWindow.cs using Guid(ce5a2489dfe724143889913694844d86) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '89d062df44fd9eb7605fb316711fb2ae') in 0.0028279 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.40 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 13 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6633 unused Assets / (4.5 MB). Loaded Objects now: 7379.
Memory consumption went from 143.4 MB to 138.9 MB.
Total: 27.417400 ms (FindLiveObjects: 1.181900 ms CreateObjectMapping: 2.663600 ms MarkObjects: 14.091700 ms  DeleteObjects: 9.478200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.559 seconds
Refreshing native plugins compatible for Editor in 1.17 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.772 seconds
Domain Reload Profiling: 3328ms
	BeginReloadAssembly (420ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (103ms)
	RebuildCommonClasses (76ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (55ms)
	LoadAllAssembliesAndSetupDomain (977ms)
		LoadAssemblies (765ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (416ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (2ms)
			BuildScriptInfoCaches (376ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1774ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1299ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (31ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (277ms)
			ProcessInitializeOnLoadAttributes (860ms)
			ProcessInitializeOnLoadMethodAttributes (107ms)
			AfterProcessingInitializeOnLoad (13ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (32ms)
Refreshing native plugins compatible for Editor in 1.36 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 13 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6640 unused Assets / (4.1 MB). Loaded Objects now: 7384.
Memory consumption went from 145.6 MB to 141.5 MB.
Total: 13.489200 ms (FindLiveObjects: 0.894200 ms CreateObjectMapping: 0.961800 ms MarkObjects: 7.440600 ms  DeleteObjects: 4.191000 ms)

Prepare: number of updated asset objects reloaded= 0
