using System.Collections.Generic;
using UnityEngine;

namespace SmartVertex.Tools
{
    /// <summary>
    /// Represents an awaitable command composed of multiple child awaitable commands, executed and undone as a unit.
    /// </summary>
    public class AwaitableCompositeCommand : IAwaitableCommand
    {
        private readonly List<IAwaitableCommand> commands;

        /// <summary>
        /// Initializes a new instance of the <see cref="AwaitableCompositeCommand"/> class with the specified commands.
        /// </summary>
        /// <param name="commands">The commands to compose and execute as a group.</param>
        public AwaitableCompositeCommand(IEnumerable<IAwaitableCommand> commands)
        {
            this.commands = new List<IAwaitableCommand>(commands);
        }

        /// <summary>
        /// Executes all child commands in order asynchronously.
        /// </summary>
        /// <returns>An awaitable task representing the asynchronous operation.</returns>
        public async Awaitable ExecuteAsync()
        {
            foreach (var command in commands)
            {
                await command.ExecuteAsync();
            }
        }

        /// <summary>
        /// Undoes all child commands in reverse order asynchronously.
        /// </summary>
        /// <returns>An awaitable task representing the asynchronous operation.</returns>
        public async Awaitable UndoAsync()
        {
            for (int i = commands.Count - 1; i >= 0; i--)
            {
                await commands[i].UndoAsync();
            }
        }
    }
}
