using System.Collections;
using System.Collections.Generic;

namespace SmartVertex.Tools
{
    /// <summary>
    /// Represents a coroutine command composed of multiple child coroutine commands, executed and undone as a unit.
    /// </summary>
    public class CoroutineCompositeCommand : ICoroutineCommand
    {
        private readonly List<ICoroutineCommand> commands;

        /// <summary>
        /// Initializes a new instance of the <see cref="CoroutineCompositeCommand"/> class with the specified commands.
        /// </summary>
        /// <param name="commands">The commands to compose and execute as a group.</param>
        public CoroutineCompositeCommand(IEnumerable<ICoroutineCommand> commands)
        {
            this.commands = new List<ICoroutineCommand>(commands);
        }

        /// <summary>
        /// Executes all child commands in order as a coroutine.
        /// </summary>
        /// <returns>An IEnumerator for coroutine execution.</returns>
        public IEnumerator ExecuteCoroutine()
        {
            foreach (var command in commands)
            {
                yield return command.ExecuteCoroutine();
            }
        }

        /// <summary>
        /// Undoes all child commands in reverse order as a coroutine.
        /// </summary>
        /// <returns>An IEnumerator for coroutine execution.</returns>
        public IEnumerator UndoCoroutine()
        {
            for (int i = commands.Count - 1; i >= 0; i--)
            {
                yield return commands[i].UndoCoroutine();
            }
        }
    }
}
